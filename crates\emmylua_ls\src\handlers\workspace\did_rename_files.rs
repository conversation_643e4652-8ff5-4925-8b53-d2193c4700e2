use std::{
    path::{Path, PathBuf},
    str::FromStr,
};

use emmylua_code_analysis::{file_path_to_uri, uri_to_file_path};
use lsp_types::{FileRename, RenameFilesParams, Uri};
use walkdir::WalkDir;

use crate::{context::ServerContextSnapshot, handlers::ClientConfig};

pub async fn on_did_rename_files_handler(
    context: ServerContextSnapshot,
    params: RenameFilesParams,
) -> Option<()> {
    dbg!(&params);
    let mut analysis = context.analysis.write().await;
    let workspace_manager = context.workspace_manager.read().await;
    dbg!(&workspace_manager.client_config);
    for file_rename in params.files {
        let FileRename { old_uri, new_uri } = file_rename;
        let mut renames = vec![];

        let old_uri = Uri::from_str(&old_uri).ok()?;
        let new_uri = Uri::from_str(&new_uri).ok()?;

        let old_path = uri_to_file_path(&old_uri)?;
        let new_path = uri_to_file_path(&new_uri)?;

        // 检查是否是Lua文件
        if is_lua_file(&old_path, &workspace_manager.client_config)
            && is_lua_file(&new_path, &workspace_manager.client_config)
        {
            renames.push((old_uri.clone(), new_uri.clone()));
        } else {
            // 有可能是目录重命名, 需要收集目录下所有 lua 文件
            if let Some(collected_renames) =
                collect_directory_lua_files(&old_path, &new_path, &workspace_manager.client_config)
            {
                renames.extend(collected_renames);
            }
        }

        // 处理所有收集到的重命名
        for (old_file_uri, new_file_uri) in renames {
            // 移除旧文件（如果存在于分析系统中）
            analysis.remove_file_by_uri(&old_file_uri);
            
            // 添加新文件
            if let Some(new_file_path) = uri_to_file_path(&new_file_uri) {
                if new_file_path.exists() {
                    if let Ok(content) = std::fs::read_to_string(&new_file_path) {
                        analysis.update_file_by_uri(&new_file_uri, Some(content));
                    }
                }
            }
        }
    }

    Some(())
}

/// 收集目录重命名后所有的Lua文件
fn collect_directory_lua_files(
    old_path: &PathBuf,
    new_path: &PathBuf,
    client_config: &ClientConfig,
) -> Option<Vec<(Uri, Uri)>> {
    // 检查新路径是否是目录（旧路径已经不存在了）
    if !new_path.is_dir() {
        return None;
    }

    let mut renames = vec![];

    // 遍历新目录下的所有Lua文件
    for entry in WalkDir::new(new_path)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| e.file_type().is_file())
    {
        let new_file_path = entry.path();

        // 检查是否是Lua文件
        if is_lua_file(new_file_path, client_config) {
            // 计算在新目录中的相对路径
            if let Ok(relative_path) = new_file_path.strip_prefix(new_path) {
                // 根据目录重命名推算出对应的旧文件路径
                let old_file_path = old_path.join(relative_path);

                // 转换为URI
                if let (Some(old_file_uri), Some(new_file_uri)) = (
                    file_path_to_uri(&old_file_path),
                    file_path_to_uri(&new_file_path.to_path_buf()),
                ) {
                    renames.push((old_file_uri, new_file_uri));
                }
            }
        }
    }

    if renames.is_empty() {
        None
    } else {
        Some(renames)
    }
}

/// 检查文件路径是否是Lua文件
fn is_lua_file(file_path: &Path, client_config: &ClientConfig) -> bool {
    let file_name = file_path.to_string_lossy();

    if file_name.ends_with(".lua") {
        return true;
    }

    // 检查客户端配置的扩展名
    for extension in &client_config.extensions {
        if file_name.ends_with(extension) {
            return true;
        }
    }

    false
}
